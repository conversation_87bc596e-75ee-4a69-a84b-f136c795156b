package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/meetstatus/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/meetstatus/service"

	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
)

type MeetingStatusHandlerImpl struct {
	meetingService service.MeetingStatusService
}

func NewMeetingStatusHandler(meetingService service.MeetingStatusService) MeetingStatusHandler {
	return &MeetingStatusHandlerImpl{
		meetingService: meetingService,
	}
}

func (h *MeetingStatusHandlerImpl) GetMeetingStatus(c *gin.Context) {
	var req dto.MeetingStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid request", err)
		apperrors.RespondError(c, appErr)
		return
	}

	res, err := h.meetingService.GetMeetingStatus(c, req)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}


	c.JSON(http.StatusOK, res)
}

func (h *MeetingStatusHandlerImpl) RegisterRoutes(r *gin.RouterGroup) {
	meetingID := r.Group("/status")
	{
		meetingID.POST("/", h.GetMeetingStatus)
	}
}
