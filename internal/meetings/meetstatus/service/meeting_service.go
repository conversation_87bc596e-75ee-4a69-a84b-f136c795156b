package service

import (
	"context"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/meetstatus/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/meetstatus/store"
)

type MeetingStatusServiceImpl struct {
	meetingStore store.MeetingStatusStore
}

func NewMeetingStatusService(meetingStore store.MeetingStatusStore) MeetingStatusService {
	return &MeetingStatusServiceImpl{
		meetingStore: meetingStore,
	}
}

func (s *MeetingStatusServiceImpl) GetMeetingStatus(ctx context.Context, body dto.MeetingStatusRequest) (dto.SuccessResponse, error) {
	meetData, err := s.meetingStore.GetMeetingStatus(ctx, body)
	if err != nil {
		return dto.SuccessResponse{
			Status:     "failed",
			Message:    "failed to get meeting status",
			Page:       body.Page,
			Limit:      body.Limit,
			TotalCount: 0,
			Data:       nil,
		}, err
	}

	response := dto.SuccessResponse{
		Status:     "success",
		Message:    "success to get meeting status",
		Page:       body.Page,
		Limit:      body.Limit,
		TotalCount: int(meetData[0].Count[0].Total),
		Data:       meetData[0].Data,
	}

	return response, nil
}
