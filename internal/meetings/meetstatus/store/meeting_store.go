package store

import (
	"context"
	"net/http"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/data"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/meetstatus/dto"

	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
)

type MeetingStatusStoreImpl struct {
	meetingJoinCollection *mongo.Collection
}

func NewMeetingStatusStore(db *mongo.Database) MeetingStatusStore {
	return &MeetingStatusStoreImpl{
		meetingJoinCollection: db.Collection(data.MeetingJoinCollectionName),
	}
}

func (s *MeetingStatusStoreImpl) GetMeetingStatus(ctx context.Context, body dto.MeetingStatusRequest) ([]dto.MeetingStatusAggResult, error) {
	var matchStage bson.D
	pipeline := mongo.Pipeline{}
	if body.StartDate != "" && body.EndDate != "" {
		matchStage = bson.D{
			{Key: "$match", Value: bson.M{
				"add_meet_date": bson.M{
					"$gte": body.StartDate,
					"$lte": body.EndDate,
				},
			}},
		}
		pipeline = append(pipeline, matchStage)
	}

	skip := (body.Page - 1) * body.Limit

	pipeline = append(pipeline, bson.D{{
		Key: "$facet",
		Value: bson.M{
			"total": bson.A{
				bson.M{"$count": "total"},
			},
			"data": bson.A{
				bson.M{"$sort": bson.M{
					"add_meet_date": -1,
				}},
				bson.M{"$skip": skip},
				bson.M{"$limit": body.Limit},
				bson.M{"$project": bson.M{
					"_id":            0,
					"meet_id":        "$meet_id",
					"add_meet_date":  "$add_meet_date",
					"meet_create_at": "$meet_create_at",
					"status":         "$status",
				}},
			},
		},
	}})

	cursor, err := s.meetingJoinCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "aggregate failed", err)
	}
	defer cursor.Close(ctx)

	var aggResults []dto.MeetingStatusAggResult
	if err := cursor.All(ctx, &aggResults); err != nil {
		return nil, apperrors.NewAppError(http.StatusInternalServerError, "decoding failed", err)
	}

	return aggResults, nil
}
