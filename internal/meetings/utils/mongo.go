package utils

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/data"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/dto"
)

func GenerateMatchStage(body dto.MeetingRequest) bson.D {
	startDateStr := body.MeetStartDate
	if startDateStr == "" {
		startDateStr = "2024-01-01"
	}

	endDateStr := body.MeetEndDate

	startTimeStr := body.MeetStartTime
	if startTimeStr == "" {
		startTimeStr = "00:00:00"
	}

	endTimeStr := body.MeetEndTime
	if endTimeStr == "" {
		endTimeStr = "23:59:59"
	}

	match := bson.M{
		"meet_department_sale": bson.M{
			"$nin": data.BlockTeamSales,
		},
	}

	// กำหนดช่วงวันที่และเวลาโดยใช้ switch เพื่อลดการซ้ำซ้อนของเงื่อนไข
	// หากไม่มี endDate ให้ match เฉพาะจาก startDate ขึ้นไป
	switch endDateStr {
	case "":
		match["add_meet_date"] = bson.M{
			"$gte": startDateStr,
		}
		match["add_meet_time"] = bson.M{
			"$gte": startTimeStr,
			"$lte": endTimeStr,
		}
	default:
		match["add_meet_date"] = bson.M{
			"$gte": startDateStr,
			"$lte": endDateStr,
		}
		match["add_meet_time"] = bson.M{
			"$gte": startTimeStr,
			"$lte": endTimeStr,
		}
	}

	// รวมฟิลด์ที่สามารถ filter แบบ multi-select (โดยใช้ $in) ไว้ใน map เดียว
	// เพื่อใช้ loop เดียวในการสร้าง match condition ลดการเขียน if ซ้ำ ๆ
	filterMap := map[string]any{
		"meet_block_name":      body.BlockCustomer,
		"meet_department_sale": body.BlockTeamSale,
		"cus_clc":              body.CustomerCLC,
		"ps_clc_name":          body.PresaleCLCName,
		"ps_visit_name":        body.PresaleVisitName,
		"plc":                  body.CustomerPLC,
		"ps_plc_name":          body.PresalePLCName,
		"status":               body.Status,
		"meeting_service":      body.MeetingService,
		"sale_name_th":         body.SaleNames,
		"privilege":            body.Privileges,
	}

	// ตรวจสอบว่า value เป็น slice ของ string และไม่ว่าง
	// จากนั้นเพิ่ม match condition แบบ $in ลงใน match
	for field, value := range filterMap {
		if arr, ok := value.([]string); ok && len(arr) > 0 {
			match[field] = bson.M{"$in": arr}
		}
	}

	// กรณีค้นหาบริษัทด้วย keyword ให้ใช้ $regex + $options: "i"
	// เพื่อให้สามารถค้นหาแบบ case-insensitive partial match
	if body.CompanyKeyword != "" {
		match["company_name_th"] = bson.M{
			"$regex":   body.CompanyKeyword,
			"$options": "i", // case-insensitive
		}
	}

	// กรณี filter ด้วย service_name (array) ให้ใช้ $elemMatch กับ StageServices
	// เพื่อให้ match document ที่มี service_name ตรงกับรายการที่เลือก
	if len(body.ServiceNames) > 0 {
		match["StageServices"] = bson.M{
			"$elemMatch": bson.M{
				"service_name": bson.M{
					"$in": body.ServiceNames,
				},
			},
		}
	}

	return bson.D{{Key: "$match", Value: match}}
}

func GenerateSort(body dto.MeetingRequest) bson.D {
	if len(body.Sort) > 0 {
		sortFields := bson.D{}
		for field, direction := range body.Sort {
			sortKey := ""
			switch field {
			case "add_meet_date":
				sortKey = "add_meet_date"
			case "add_meet_time":
				sortKey = "add_meet_time"
			case "ps_clc_name":
				sortKey = "ps_clc_name"
			case "ps_visit_name":
				sortKey = "ps_visit_name"
			case "ps_plc_name":
				sortKey = "ps_plc_name"
			case "sale_name_th":
				sortKey = "sale_name_th"
			case "block":
				sortKey = "meet_block_name"
			case "block_team_sale":
				sortKey = "meet_department_sale"
			case "meet_status":
				sortKey = "meet_status"
			case "sum_grade":
				sortKey = "meet_sum_grade"
			case "meeting_channel":
				sortKey = "site"
			case "sale_type_name":
				sortKey = "sale_type_name"
			case "ps_privilege_name":
				sortKey = "ps_privilege_name"
			default:
				continue
			}

			sortFields = append(sortFields, bson.E{Key: sortKey, Value: direction})
		}

		if len(sortFields) > 0 {
			sortFields = append(sortFields, bson.E{Key: "meet_id", Value: -1})
			return bson.D{{Key: "$sort", Value: sortFields}}
		}
	}

	return bson.D{{Key: "$sort", Value: bson.M{"meet_datetime": -1, "meet_id": -1}}}
}

// First คืนค่า MongoDB aggregation expression สำหรับการดึงค่าตัวแรกของ field ที่ระบุ
// ใช้ร่วมกับ $group stage เพื่อเลือกค่าตัวแทนจากแต่ละกลุ่ม
//
// Parameters:
//   - field: ชื่อฟิลด์ที่ต้องการดึงค่าตัวแรก (ไม่ต้องใส่ $ นำหน้า)
//
// Example:
//
//	"cus_id": utils.First("cus_id")
//
// Result:
//
//	{ "$first": "$cus_id" }
func First(field string) bson.M {
	return bson.M{"$first": fmt.Sprintf("$%s", field)}
}

// DateFormat แปลง field วันที่ (string) ให้อยู่ในรูปแบบ dd/mm/yyyy
// ใช้ร่วมกับ MongoDB aggregation stage ($dateFromString + $dateToString)
func DateFormat(fieldName string) bson.M {
	return bson.M{
		"$dateToString": bson.M{
			"format": "%d/%m/%Y",
			"date": bson.M{
				"$dateFromString": bson.M{
					"dateString": "$" + fieldName,
				},
			},
		},
	}
}
