package handler

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/dto"
	"git.inet.co.th/meeting-dashboard-backend/internal/meetings/v4/service"
	"git.inet.co.th/meeting-dashboard-backend/internal/middleware"

	apperrors "git.inet.co.th/meeting-dashboard-backend/pkg/error"
)

type MeetingHandlerImpl struct {
	meetingService service.MeetingService
	authMiddleware *middleware.AuthMiddleware
}

func NewMeetingHandler(meetingService service.MeetingService, authMiddleware *middleware.AuthMiddleware) MeetingHandler {
	return &MeetingHandlerImpl{
		meetingService: meetingService,
		authMiddleware: authMiddleware,
	}
}

func (h *MeetingHandlerImpl) FetchMeetingData(c *gin.Context) {
	var req dto.MeetingRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid request", err)
		apperrors.RespondError(c, appErr)
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}

	res, err := h.meetingService.FetchMeetingData(c, req)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}

	response := dto.SuccessResponse{
		Status:     "success",
		Message:    "Fetch meeting data successfully",
		Data:       res.Data,
		TotalCount: res.TotalCount,
		Page:       req.Page,
		Limit:      req.Limit,
	}

	c.JSON(http.StatusOK, response)
}

func (h *MeetingHandlerImpl) ExportDataToExcel(c *gin.Context) {
	var req dto.MeetingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		appErr := apperrors.NewAppError(http.StatusBadRequest, "invalid request", err)
		apperrors.RespondError(c, appErr)
		return
	}

	filename := "meeting.xlsx"

	excelBytes, err := h.meetingService.ExportDataToExcel(c, req, filename)
	if err != nil {
		appErr := apperrors.MapError(err)
		apperrors.RespondError(c, appErr)
		return
	}

	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelBytes)
}

func (h *MeetingHandlerImpl) RegisterRoutes(r *gin.RouterGroup) {
	// r.Use(h.authMiddleware.RequireAuth())

	//Meeting Data
	r.POST("", h.FetchMeetingData)
	r.POST("/excel", h.ExportDataToExcel)
}
