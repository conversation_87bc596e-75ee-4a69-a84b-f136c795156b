package store

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"git.inet.co.th/meeting-dashboard-backend/internal/migration/data"
)

const (
	MeetingCollectionName       = "meeting"
	MeetingPeriodCollectionName = "meeting_period"
	MeetingJoinCollectionName   = "meeting_join"
)

type MigrateStoreImpl struct {
	meetingCollection       *mongo.Collection
	meetingPeriodCollection *mongo.Collection
	meetingJoinCollection   *mongo.Collection
}

func NewMigrateStore(db *mongo.Database) MigrateStore {
	return &MigrateStoreImpl{
		meetingCollection:       db.Collection(MeetingCollectionName),
		meetingPeriodCollection: db.Collection(MeetingPeriodCollectionName),
		meetingJoinCollection:   db.Collection(MeetingJoinCollectionName),
	}
}

func (s *MigrateStoreImpl) UpsertInfoData(ctx context.Context, data []any) error {
	var upsertErrors []error

	for _, item := range data {
		filter := bson.M{
			"meet_id": item.(map[string]any)["meet_id"],
		}

		update := bson.M{
			"$set": item,
		}

		opts := options.Update().SetUpsert(true)

		_, err := s.meetingCollection.UpdateOne(ctx, filter, update, opts)
		if err != nil {
			upsertErrors = append(upsertErrors, fmt.Errorf("error upserting document: %w", err))
		}
	}

	if len(upsertErrors) > 0 {
		return fmt.Errorf("upsert errors: %v", upsertErrors)
	}

	return nil
}

func (s *MigrateStoreImpl) UpsertPeriodData(ctx context.Context, data []any) error {
	var upsertErrors []error

	for _, item := range data {
		filter := bson.M{
			"meet_id": item.(map[string]any)["meet_id"],
		}

		update := bson.M{
			"$set": item,
		}

		opts := options.Update().SetUpsert(true)

		_, err := s.meetingPeriodCollection.UpdateOne(ctx, filter, update, opts)
		if err != nil {
			upsertErrors = append(upsertErrors, fmt.Errorf("error upserting document: %w", err))
		}
	}

	if len(upsertErrors) > 0 {
		return fmt.Errorf("upsert errors: %v", upsertErrors)
	}

	return nil
}

func (s *MigrateStoreImpl) PreJoinMeeting(ctx context.Context) error {
	var (
		partnerTypeSwitch = bson.M{
			"$switch": bson.M{
				"branches": bson.A{
					bson.M{
						"case": bson.M{
							"$and": bson.A{
								bson.M{"$ne": bson.A{data.MEET_BLOCK_NAME, nil}},
								bson.M{"$ne": bson.A{data.MEET_BLOCK_NAME, "-"}},
								bson.M{"$in": bson.A{data.MEET_BLOCK_NAME, data.EXISTING_GROUP_ARRs}},
							},
						},
						"then": data.EXISTING_GROUP,
					},
					bson.M{
						"case": bson.M{"$in": bson.A{data.PRIVATE_CTB, data.NEW_PARTNER_GROUP_ARRs}},
						"then": data.NEW_PARTNER_GROUP,
					},
					bson.M{
						"case": bson.M{"$in": bson.A{data.PRIVATE_CTB, data.NEW_CUSTOMER_GROUP_ARRs}},
						"then": data.NEW_CUSTOMER_GROUP,
					},
				},
				"default": "-",
			},
		}

		saleTypeSwitch = bson.M{
			"$switch": bson.M{
				"branches": bson.A{
					bson.M{"case": bson.M{"$eq": bson.A{"$sale_type_name", "PLC"}}, "then": "PLC"},
					bson.M{"case": bson.M{"$eq": bson.A{"$sale_type_name", "CLC"}}, "then": "CLC"},
					bson.M{"case": bson.M{"$eq": bson.A{"$sale_type_name", "Update Service"}}, "then": "-"},
					bson.M{"case": bson.M{"$eq": bson.A{"$sale_type_name", "เหตุผลอื่น ๆ"}}, "then": "-"},
				},
				"default": "",
			},
		}

		psPrivilegeNameSwitch = bson.M{
			"$switch": bson.M{
				"branches": bson.A{
					bson.M{"case": bson.M{"$eq": bson.A{"$sale_type_name", "PLC"}}, "then": "$ps_plc_name"},
					bson.M{"case": bson.M{"$eq": bson.A{"$sale_type_name", "CLC"}}, "then": "$ps_clc_name"},
					bson.M{"case": bson.M{"$eq": bson.A{"$sale_type_name", "Update Service"}}, "then": "-"},
					bson.M{"case": bson.M{"$eq": bson.A{"$sale_type_name", "เหตุผลอื่น ๆ"}}, "then": "-"},
				},
				"default": "",
			},
		}
	)

	var (
		lookupMetadata = bson.D{{Key: "$lookup", Value: bson.D{
			{Key: "from", Value: "meeting_metadata"},
			{Key: "localField", Value: "meet_id"},
			{Key: "foreignField", Value: "meet_id"},
			{Key: "as", Value: "metadata"},
		}}}

		unwindMetadata = bson.D{{Key: "$unwind", Value: bson.D{
			{Key: "path", Value: "$metadata"},
			{Key: "preserveNullAndEmptyArrays", Value: true},
		}}}

		addFieldsFromMetadata = bson.D{{Key: "$addFields", Value: bson.D{
			{Key: "status", Value: bson.D{{Key: "$ifNull", Value: bson.A{"$metadata.status", "-"}}}},
			{Key: "comment", Value: bson.D{{Key: "$ifNull", Value: bson.A{"$metadata.comment", "-"}}}},
			{Key: "meeting_service", Value: bson.D{{Key: "$ifNull", Value: bson.A{"$metadata.meeting_service", bson.A{}}}}},
		}}}

		projectDrop = bson.D{{Key: "$project", Value: bson.D{
			{Key: "metadata", Value: 0},
			{Key: "_id", Value: 0},
		}}}

		computeDerived = bson.D{{Key: "$addFields", Value: bson.M{
			"partner_type":      partnerTypeSwitch,
			"privilege":         saleTypeSwitch,
			"ps_privilege_name": psPrivilegeNameSwitch,
		}}}

		mergeToJoin = bson.D{{Key: "$merge", Value: bson.D{
			{Key: "into", Value: "meeting_join"},
			{Key: "on", Value: "meet_id"},
			{Key: "whenMatched", Value: "merge"},
			{Key: "whenNotMatched", Value: "insert"},
		}}}
	)

	pipeline := mongo.Pipeline{
		lookupMetadata,
		unwindMetadata,
		addFieldsFromMetadata,
		projectDrop,
		computeDerived,
		mergeToJoin,
	}

	if _, err := s.meetingCollection.Aggregate(ctx, pipeline); err != nil {
		return fmt.Errorf("Aggregate PreJoinMeeting failed: %w", err)
	}

	return nil
}

func (s *MigrateStoreImpl) PreJoinMeetingStatus(ctx context.Context) error {
	// Get today's date in the same format used by the system
	today := time.Now().Format(time.DateOnly)

	// Create filter to find documents with status "-" and today's date
	filter := bson.M{
		"meet_create_at": today,
		"status":         "-",
		"meet_status": bson.M{
			"$ne": nil,
		},
	}

	// Create update to set status to "Ongoing"
	update := bson.M{
		"$set": bson.M{
			"status": "Ongoing",
		},
	}

	// Execute the update operation
	_, err := s.meetingJoinCollection.UpdateMany(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update meeting status: %w", err)
	}

	return nil
}

func (s *MigrateStoreImpl) PreJoinMeetingPeriod(ctx context.Context) error {
	lookupPeriod := bson.D{{Key: "$lookup", Value: bson.D{
		{Key: "from", Value: "meeting_period"},
		{Key: "localField", Value: "meet_id"},
		{Key: "foreignField", Value: "meet_id"},
		{Key: "as", Value: "metadata"},
	}}}

	unwindPeriod := bson.D{{Key: "$unwind", Value: bson.D{
		{Key: "path", Value: "$metadata"},
		{Key: "preserveNullAndEmptyArrays", Value: true},
	}}}

	addFieldsFromPeriod := bson.D{{Key: "$addFields", Value: bson.D{
		{Key: "sale_mom_desc", Value: bson.D{{Key: "$ifNull", Value: bson.A{"$metadata.sale_mom_desc", "-"}}}},
		{Key: "presale_mom_desc", Value: bson.D{{Key: "$ifNull", Value: bson.A{"$metadata.presale_mom_desc", "-"}}}},
		{Key: "StageServices", Value: bson.D{{Key: "$ifNull", Value: bson.A{"$metadata.StageServices", bson.A{}}}}},
	}}}

	projectDropPeriod := bson.D{{Key: "$project", Value: bson.D{
		{Key: "metadata", Value: 0},
		{Key: "_id", Value: 0},
	}}}

	mergePeriod := bson.D{{Key: "$merge", Value: bson.D{
		{Key: "into", Value: "meeting_join"},
		{Key: "on", Value: "meet_id"},
		{Key: "whenMatched", Value: "merge"},
		{Key: "whenNotMatched", Value: "insert"},
	}}}

	pipeline := mongo.Pipeline{
		lookupPeriod,
		unwindPeriod,
		addFieldsFromPeriod,
		projectDropPeriod,
		mergePeriod,
	}

	_, err := s.meetingCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return err
	}

	return nil
}

func (s *MigrateStoreImpl) CountMeetingData(ctx context.Context) (int64, error) {
	countPipeline := mongo.Pipeline{
		bson.D{{Key: "$count", Value: "total"}},
	}

	countCursor, err := s.meetingCollection.Aggregate(ctx, countPipeline)
	if err != nil {
		return 0, err
	}
	var countResult struct {
		Total int `bson:"total"`
	}
	if countCursor.Next(ctx) {
		if err := countCursor.Decode(&countResult); err != nil {
			return 0, err
		}
	}

	return int64(countResult.Total), nil
}

func (r *MigrateStoreImpl) CountByTodayMeetCreateAt(ctx context.Context) (int64, string, error) {
	today := time.Now().Format(time.DateOnly)

	// Filter ตาม meet_create_at = วันนี้
	filter := bson.M{"meet_create_at": today}

	count, err := r.meetingCollection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, "", fmt.Errorf("count error: %w", err)
	}

	return count, today, nil
}
